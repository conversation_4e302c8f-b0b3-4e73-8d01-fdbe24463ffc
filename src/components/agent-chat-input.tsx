import { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";

import type { FileData } from "../types/file-types";
import { FileSystemContext } from "../context/file-system-context";
import { FileAutocomplete } from "./agent-file-autocomplete";

interface ChatInputProps {
  onSend: (content: string) => void;
  onFileMention: (file: FileData) => void;
  disabled?: boolean;
}

export function ChatInputWithMention({ onSend, onFileMention, disabled }: ChatInputProps) {
  const { allFiles } = useContext(FileSystemContext);
  const [value, setValue] = useState("");
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [autocompleteQuery, setAutocompleteQuery] = useState("");
  const [caretPosition, setCaretPosition] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const caret = e.target.selectionStart ?? newValue.length;
    setValue(newValue);
    setCaretPosition(caret);

    const beforeCaret = newValue.slice(0, caret);
    const mentionMatch = beforeCaret.match(/@([^\s]*)$/);
    if (mentionMatch) {
      setAutocompleteQuery(mentionMatch[1]);
      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if ((e.key === 'Enter' && !e.shiftKey)) {
      e.preventDefault();
      if (!disabled && value.trim()) {
        onSend(value);
        setValue("");
        setShowAutocomplete(false);
      }
    }
  };

  const insertFileMention = useCallback((file: FileData) => {
    const beforeCaret = value.slice(0, caretPosition);
    const afterCaret = value.slice(caretPosition);
    const atPosition = beforeCaret.lastIndexOf('@');
    const beforeAt = value.slice(0, atPosition);
    const mentionText = `@${file.path}`;
    const newValue = beforeAt + mentionText + afterCaret;
    setValue(newValue);
    setShowAutocomplete(false);
    onFileMention(file);
    textareaRef.current?.focus();
  }, [value, caretPosition, onFileMention]);

  // Compute dropdown position relative to textarea and caret
  const position = useMemo(() => {
    const el = textareaRef.current; if (!el) return { top: 0, left: 0 };
    const rect = el.getBoundingClientRect();
    return { top: rect.top + window.scrollY + 24, left: rect.left + window.scrollX + 16 };
  }, [showAutocomplete]);

  return (
    <div className="agent-chat-input-container">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="Type @ to mention files..."
        className="agent-chat-input"
        disabled={disabled}
      />
      {showAutocomplete && (
        <FileAutocomplete
          query={autocompleteQuery}
          onSelect={insertFileMention}
          onClose={() => setShowAutocomplete(false)}
          position={position}
          allFiles={allFiles}
        />
      )}
    </div>
  );
}

export default ChatInputWithMention;

