import { memo, useCallback } from "react";

interface PackedContentDetail {
  content: string;
  tokenEstimate?: number;
  files?: number;
  metadata?: Record<string, unknown>;
}

interface SendToAgentButtonProps {
  enabled: boolean;
  packed: PackedContentDetail | null | undefined;
  className?: string;
}

export const SendToAgentButton = memo(function SendToAgentButton({ enabled, packed, className }: SendToAgentButtonProps) {
  const onClick = useCallback(() => {
    if (!enabled || !packed) return;
    try {
      const evt = new CustomEvent<PackedContentDetail>("pasteflow:send-to-agent", { detail: packed });
      window.dispatchEvent(evt);
    } catch {
      // no-op
    }
  }, [enabled, packed]);

  return (
    <button
      type="button"
      className={className ? className : "preview-button"}
      disabled={!enabled || !packed}
      onClick={onClick}
      title={enabled ? "Send current packed content to <PERSON>" : "Pack content to enable sending"}
      aria-label="Send packed content to <PERSON>"
      data-testid="send-to-agent"
    >
      <span>Send to Agent</span>
    </button>
  );
});

export default SendToAgentButton;

