import { useEffect, useMemo, useState } from "react";

import type { FileData } from "../types/file-types";

interface FileAutocompleteProps {
  query: string;
  onSelect: (file: FileData) => void;
  onClose: () => void;
  position: { top: number; left: number };
  allFiles: FileData[];
}

export function FileAutocomplete({ query, onSelect, onClose, position, allFiles }: FileAutocompleteProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const files = useMemo(() => allFiles.filter(f => !f.isDirectory), [allFiles]);

  const filteredFiles = useMemo(() => {
    const q = query.trim().toLowerCase();
    const arr = q ? files.filter(f => f.path.toLowerCase().includes(q)) : files;
    return arr.slice(0, 10);
  }, [files, query]);

  useEffect(() => setSelectedIndex(0), [query]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    switch (e.key) {
    case 'ArrowDown':
      e.preventDefault();
      setSelectedIndex((i) => Math.min(i + 1, filteredFiles.length - 1));
      break;
    case 'ArrowUp':
      e.preventDefault();
      setSelectedIndex((i) => Math.max(i - 1, 0));
      break;
    case 'Enter':
    case 'Tab':
      e.preventDefault();
      if (filteredFiles[selectedIndex]) onSelect(filteredFiles[selectedIndex]);
      break;
    case 'Escape':
      onClose();
      break;
    }
  };

  return (
    <div
      className="agent-file-autocomplete"
      style={{ top: position.top, left: position.left, position: 'absolute' }}
      onKeyDown={handleKeyDown}
      role="listbox"
    >
      {filteredFiles.map((file, index) => (
        <div
          key={file.path}
          className={`agent-autocomplete-item ${index === selectedIndex ? 'selected' : ''}`}
          onMouseEnter={() => setSelectedIndex(index)}
          onMouseDown={(e) => { e.preventDefault(); onSelect(file); }}
          role="option"
          aria-selected={index === selectedIndex}
        >
          <div className="file-path">{file.path}</div>
          <div className="file-meta">{(file.tokenCount ?? 0).toString()} tokens</div>
        </div>
      ))}
    </div>
  );
}

export default FileAutocomplete;

