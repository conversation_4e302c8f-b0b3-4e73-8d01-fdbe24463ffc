import { useChat } from "@ai-sdk/react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import type { FileData } from "../types/file-types";
import { FileSystemContext } from "../context/file-system-context";
import { ChatInputWithMention } from "./agent-chat-input";

type LineRange = { start: number; end: number };

export type AgentFileContext = {
  path: string;
  content: string;
  tokenCount?: number;
  lines?: LineRange[] | null;
};

export function AgentPanel() {
  const fs = (window as any).__PF_FS || FileSystemContext;
  // Local dynamic context store
  const [dynamicContext, setDynamicContext] = useState<Map<string, AgentFileContext>>(new Map());

  const { messages, sendMessage, status, append } = useChat({
    api: "/api/v1/chat",
    onBeforeSend: (message) => {
      const context = Array.from(dynamicContext.values());
      return { ...message, context } as any;
    },
  });

  const loadFileContent = useCallback(async (path: string, lines?: LineRange[]) => {
    // For Phase 1, fetch via existing file content API
    try {
      const u = new URL("/api/v1/files/content", window.location.origin);
      u.searchParams.set("path", path);
      if (lines && lines.length > 0) u.searchParams.set("lines", JSON.stringify(lines));
      const token = (window as any).__PF_AUTH || window.localStorage.getItem("pf.auth") || "";
      const res = await fetch(u.toString(), { headers: { Authorization: token } });
      if (!res.ok) throw new Error("Failed to load file content");
      const data = await res.json();
      const content: string = data?.data?.content || "";
      const tokenCount: number | undefined = data?.data?.tokenCount;
      return { path, content, tokenCount, lines: lines || null } as AgentFileContext;
    } catch {
      return { path, content: "", tokenCount: 0, lines: lines || null } as AgentFileContext;
    }
  }, []);

  // Receive packed content from ContentArea
  useEffect(() => {
    const handle = (event: Event) => {
      const ce = event as CustomEvent<{ content: string; tokenEstimate?: number; files?: number; metadata?: any }>;
      const packed = ce.detail;
      // Format a simple message and append
      append({
        role: "user" as any,
        content: packed?.content || "",
        metadata: { type: "packed", tokens: packed?.tokenEstimate, files: packed?.files },
      } as any);
    };
    window.addEventListener("pasteflow:send-to-agent", handle as any);
    return () => window.removeEventListener("pasteflow:send-to-agent", handle as any);
  }, [append]);

  const handleFileMention = useCallback(async (file: FileData, lines?: LineRange[]) => {
    const ctx = await loadFileContent(file.path, lines);
    setDynamicContext((prev) => new Map(prev).set(file.path, ctx));
  }, [loadFileContent]);

  // Minimal rendering for Phase 1
  return (
    <div className="agent-panel" aria-label="Agent Panel">
      <div className="agent-messages">
        {messages.map((m, i) => (
          <div key={i} className={`agent-msg agent-${(m as any).role}`}>
            {(m as any).content}
          </div>
        ))}
      </div>
      <ChatInputWithMention onSend={sendMessage} onFileMention={handleFileMention} disabled={status !== "ready"} />
    </div>
  );
}

export default AgentPanel;

