# Phase 1 Implementation Guide — Vercel AI SDK v5: Core Chat & Tools

This guide provides an implementation-ready, step-by-step plan to deliver Phase 1 of the Vercel AI SDK v5 integration in PasteFlow. It is scoped to Core Chat and Tools only and is designed for a senior engineer familiar with the codebase structure and conventions.

Sections
- Executive Summary
- Prerequisites
- Implementation Tasks
- File Tree Compatibility
- Code Examples
- Testing Strategy
- Acceptance Criteria
- Future Phase Context

Notes
- All file paths are workspace-relative. Code uses TypeScript, React, and Electron conventions already present in the repository.
- Where code blocks are taken from the primary plan, they are preserved exactly as-is in triple backticks.


**Executive Summary**
- Goal: Ship a minimal but robust agent loop using Vercel AI SDK v5 with server-side streaming, a new `/api/v1/chat` route, and essential tool stubs.
- Deliverables:
  - Install `ai`, `@ai-sdk/openai`, `@ai-sdk/react` dependencies.
  - Implement `POST /api/v1/chat` using `streamText`, `convertToModelMessages`, `pipeDataStreamToResponse(res)`.
  - Stub main-process tools: `file` (read/info), `search` (filename fallback only), `context` (summary of provided context).
  - Add `AgentPanel` React component powered by `@ai-sdk/react` `useChat` with basic `@-mention` insertion.
  - Add `SendToAgentButton` to dispatch packed content into the AgentPanel via `CustomEvent`.
- Security & performance: enforce auth, workspace path validation, safe file reads, conservative token & byte limits, simple rate limiting guard, and streaming with correct HTTP headers.
- Integration points: `src/main/api-server.ts` (HTTP server), `src/main/file-service.ts` (path validation & reads), `src/services/token-service-main.ts` (token counts), and new renderer components under `src/components/`.


**Prerequisites**
- Node.js and Electron per repository defaults.
- PasteFlow codebase conventions (TypeScript, Jest, ESLint, zod for request validation).
- Active workspace: many file operations require allowed workspace paths to be set via the existing workspace management flows.
- Environment variables:
  - `OPENAI_API_KEY` must be configured for `@ai-sdk/openai` provider.
  - Electron renderer can rely on main-process server running on its internal host; use `Authorization` header handled by existing middleware in `src/main/api-server.ts`.
- Existing utilities leveraged:
  - `validateAndResolvePath` and `readTextFile`: `src/main/file-service.ts`.
  - `getMainTokenService`: `src/services/token-service-main.ts`.
  - HTTP server class and security middleware: `src/main/api-server.ts`.
- Assumptions:
  - Server uses Express with an authorization middleware that validates tokens for all routes.
  - Renderer uses Vite and React 18; UI components use PasteFlow’s double-quoted strings, kebab-case file naming, and import ordering rules.


**Implementation Tasks**

- Install Vercel AI SDK v5 dependencies.
  - Add runtime deps: `ai`, `@ai-sdk/openai`, `@ai-sdk/react` (use `ai@^5`, `@ai-sdk/openai@^2`, `@ai-sdk/react@^2`).
  - Verify no conflicting legacy `ai/react` import paths; only `@ai-sdk/react` is used for `useChat`.
- Implement `POST /api/v1/chat` using Vercel AI SDK v5.
  - Import `streamText`, `convertToModelMessages` from `ai` and `openai` from `@ai-sdk/openai`.
  - Validate request body, enforce auth (already handled globally), and stream response using `pipeDataStreamToResponse(res)`.
  - Include minimal tool stubs: `file` (read/info), `search` (filename match fallback), `context` (summary). Use `zod` input schemas.
  - Use existing workspace validation and file read utilities for safety.
  - Optionally implement a tiny rate limiter (in-memory) at route level to prevent spiky misuse in dev.
- Add AgentPanel in renderer using `@ai-sdk/react useChat`.
  - Accept packed content via `CustomEvent('pasteflow:send-to-agent', { detail })`.
  - Provide `@-mention` insertion and display of dynamic context cards, leaving advanced behaviors for later phases.
- Add AgentPanel file selection (separate from main file tree)
  - Implement an `@` file autocomplete inside the Agent chat input that filters workspace files and lets users insert file mentions.
  - Maintain a local Agent-only selection (attachments) list; do not use or mutate the main content area selection.
  - Show a mini file list within the Agent Panel (chips/cards) of attached files with remove/edit-lines controls.
  - Attach selected files to the next outgoing message (and optionally pin across messages) via `useChat`’s `onBeforeSend`.
- Add SendToAgentButton.
  - Button located in the content packing area; enabled only when content is packed.
  - Dispatches packed content to the Agent Panel and optionally auto-submits a formatted message.
- Ensure security and validation:
  - `validateAndResolvePath` used for every tool path input.
  - File reads respect size limits and binary detection from `readTextFile`.
  - Only filename search (no ripgrep) in Phase 1.


**File Tree Compatibility**

Goal: Implement Phase 1 without any regression to the existing file tree selection UX (checkboxes, partial/indeterminate states, auto-select of folder contents, and auto-expand on folder select).

- Non-invasive integration: Do not modify or refactor these modules in Phase 1
  - `src/components/tree-item.tsx`
  - `src/components/virtualized-tree.tsx`
  - `src/components/sidebar.tsx`
  - `src/hooks/use-file-selection-state.ts`
  - `src/utils/selection-cache.ts` and `src/utils/folder-selection-index.ts`
- Agent UI isolation
  - Implement new components under `src/components/agent-*` and scope styles with an `agent-` prefix to avoid CSS collisions with `.tree-item*` or `.file-list*` selectors.
  - Mount the Agent UI in its own container (e.g., right panel or modal host). Do not render it inside the Sidebar tree container to avoid event/click handling interference.
- Separate selection stores
  - The Agent Panel uses its own attachment store (e.g., `pendingAttachments`/`pinnedAttachments`) and must never call `toggleFileSelection`/`toggleFolderSelection` from the main selection hooks.
  - The Agent’s @-file mentions only affect the agent chat context for the next message (or for pinned attachments) and do not change `selectedFiles` used by the main content area/file tree.
- Content-area integration
  - Insert `SendToAgentButton` in `src/components/content-area.tsx` only in the packed “Ready” state, next to the existing Copy/Preview controls. It must not alter selection or tree state.
  - The button only dispatches a `CustomEvent('pasteflow:send-to-agent', { detail })` and never mutates file selection.
- Events and keyboard handling
  - Keep all new keyboard handlers local to the Agent input controls. Do not add global `keydown` listeners; do not intercept Space/Enter which are used by tree toggles and checkboxes.
  - When adding any document/window listeners (e.g., to receive `pasteflow:send-to-agent`), always add/remove them inside a React `useEffect` cleanup.
- Feature flag gating (recommended)
  - Add `FEATURES.AGENT_PANEL_ENABLED` and `FEATURES.SEND_TO_AGENT_ENABLED` in `src/constants/app-constants.ts` and gate rendering with `const features = (window as any).__PF_FEATURES \?\? FEATURES;` to allow quick disable in case of issues.
  - Default both to `true` in dev; CI can override via `window.__PF_FEATURES` in tests.
- Server route safety
  - Register `/api/v1/chat` without altering existing middleware order. Keep the global auth and JSON body parser unchanged.
  - Prefer adding the route near other API routes inside `registerRoutes()`; do not remove or reorder selection/content routes.


**Implementation Tasks — Detailed**

- Install Vercel AI SDK v5 deps: ai, @ai-sdk/openai, @ai-sdk/react
  - Update `package.json` dependencies to include:
    - `"ai": "^5"`
    - `"@ai-sdk/openai": "^2"`
    - `"@ai-sdk/react": "^2"`
  - Commands:
    - `npm install ai @ai-sdk/openai @ai-sdk/react`
  - Verify: no legacy `ai/react` usage; only `@ai-sdk/react` is imported for hooks.

- Implement POST /api/v1/chat with streamText + convertToModelMessages and pipeDataStreamToResponse(res)
  - Extend `src/main/api-server.ts` to register a new route inside `registerRoutes()` and implement a handler method.
  - The handler:
    - Extracts `{ messages, context }` from `req.body`.
    - Uses `streamText` with `openai("gpt-5-mini")` by default (tunable).
    - Converts UI messages using `convertToModelMessages(messages)`.
    - Defines Phase 1 tools: `file`, `search`, `context` (all `zod`-validated).
    - Pipes the data stream to the Express response.
  - Headers are handled by `pipeDataStreamToResponse(res)`. If you manually stream, apply SSE headers (see preserved snippet below).
  - Add a small in-memory rate limiter for this route (e.g., 10 requests/30s per auth token) to prevent abuse during dev without affecting other routes.
  - Electron main runtime notes:
    - Keep static ESM imports for `ai`, `@ai-sdk/openai`, and `zod`; the tsup build already handles these.
    - Use the provided `nodeRequire` helper only for Node built-ins within tool implementations (e.g., `node:fs`, `node:path`).

- Stub main-process tools: file (read/info), search (filename fallback), context (summary)
  - `file.read`: validates path within workspace; reads text; counts tokens; rejects directories and binary files.
  - `file.info`: validates path; returns `stat` metadata and token counts for text files.
  - `search.files`: substring match across allowed workspace; recursive directory walk with guardrails and cap on max results.
  - `context.summary`: summarizes the dynamic context passed by the renderer (`req.body.context`).

- Add AgentPanel with @ai-sdk/react useChat and basic @-mention insertion
  - Create `src/components/agent-panel.tsx` using `useChat` with `api: '/api/v1/chat'`.
  - Implement simple `@` detection and show `agent-file-autocomplete` dropdown.
  - Maintain a `Map<string, FileContext>` for dynamically added context.
  - Listen for `pasteflow:send-to-agent` to receive packed content.
  - Containment & styling safeguards:
    - Prefix all classes with `agent-` and avoid generic selectors.
    - Scope overlays/menus to a local portal container to prevent z-index conflicts with the Sidebar.
    - Avoid global key handlers; handle keys only in input components and clean up listeners on unmount.
  
- Agent Panel file selection (message-scoped attachments)
  - Add an `AgentAttachmentList` in the panel to show currently attached files (chips/cards) with remove and optional line-range edit.
  - Keep two local states:
    - `pendingAttachments` (Map keyed by absolute path) applied to the next `sendMessage` only.
    - `pinnedAttachments` (optional) that persist across messages when the user enables “Pin files to thread”.
  - `onBeforeSend` should assemble attachments = pending + pinned (deduped) and attach as `context` for the outgoing message.
  - After send:
    - Clear `pendingAttachments`.
    - Leave `pinnedAttachments` intact if the “Pin” toggle is on; else clear it too.
  - Do not call global selection APIs; load preview/token estimates via existing file data when available or via a safe file-read helper.

- Add SendToAgentButton to dispatch packed content event to AgentPanel
  - Create `src/components/send-to-agent-button.tsx` that:
    - Checks whether content is packed and has a signature/token estimate.
    - Dispatches `pasteflow:send-to-agent` with the packed details.
  - Insert in `src/components/content-area.tsx` near the pack controls; only render when content is packed.
  - Safety constraints:
    - Do not import selection hooks here; do not mutate `selectedFiles` or `expandedNodes`.
    - Guard rendering behind `FEATURES.SEND_TO_AGENT_ENABLED`.


**Code Examples**

- Note: The following blocks that originate from the main plan are preserved exactly as they appear in the source plan to maintain alignment with the architectural direction. Additional blocks (e.g., server handler, button) are Phase 1–scoped and implementation-ready for PasteFlow.

- Streaming and SSE headers (Express) — preserved from the plan
```typescript
// In an Express handler, if you stream manually:
res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
// Then pipe the data stream (prefer the SDK helper below):
return result.pipeDataStreamToResponse(res);
```

- Core Agent Component (`src/components/agent-panel.tsx`) — preserved from the plan
```typescript
import { useChat } from '@ai-sdk/react';
import { useState, useCallback, useRef } from 'react';
import { MiniFileList } from './agent-mini-file-list';
import { ChatInputWithMention } from './agent-chat-input';
import { FileAutocomplete } from './agent-file-autocomplete';

export function AgentPanel() {
  const [dynamicContext, setDynamicContext] = useState<Map<string, FileContext>>(new Map());
  const [showMiniFileList, setShowMiniFileList] = useState(true);

  const { messages, sendMessage, status, append } = useChat({
    // Prefer built-in fetch transport; ensure SDK version supports this.
    api: '/api/v1/chat',

    onBeforeSend: (message) => {
      // Attach dynamic context to message
      return {
        ...message,
        context: Array.from(dynamicContext.values())
      };
    }
  });

  // Listen for incoming packed content from main UI
  useEffect(() => {
    const handlePackedContent = (event: CustomEvent<PackedContent>) => {
      const packed = event.detail;

      // Format and auto-submit the packed content to current chat
      const message = formatPackedContent(packed);

      // This adds to the current thread (or starts a new one if empty)
      append({
        role: 'user',
        content: message,
        metadata: {
          type: 'packed-context',
          files: packed.files.length,
          tokens: packed.metadata.totalTokens
        }
      });

      // Store for reference
      setInitialContext(packed);
    };

    window.addEventListener('pasteflow:send-to-agent', handlePackedContent);
    return () => window.removeEventListener('pasteflow:send-to-agent', handlePackedContent);
  }, [append]);

  const handleFileSelect = useCallback((file: FileData) => {
    setDynamicContext(prev => new Map(prev).set(file.path, {
      path: file.path,
      content: file.content,
      tokenCount: file.tokenCount,
      lines: null
    }));
  }, []);

  const handleFileMention = useCallback(async (path: string, lines?: LineRange) => {
    const fileContent = await loadFileContent(path, lines);
    setDynamicContext(prev => new Map(prev).set(path, fileContent));
  }, []);

  return (
    <div className="agent-panel">
      <AgentHeader>
        <TokenDisplay
          initial={initialContext?.tokenCount || 0}
          dynamic={calculateDynamicTokens(dynamicContext)}
        />
      </AgentHeader>

      <div className="agent-workspace">
        {/* Mini file browser for quick access */}
        <MiniFileList
          files={workspaceFiles}
          selected={Array.from(dynamicContext.keys())}
          onSelect={handleFileSelect}
          collapsed={!showMiniFileList}
        />

        <div className="agent-chat-area">
          {/* Context cards showing selected files */}
          <ContextCards>
            {Array.from(dynamicContext.values()).map(ctx => (
              <FileCard
                key={ctx.path}
                file={ctx}
                onRemove={() => removeDynamicContext(ctx.path)}
                onEditLines={(lines) => updateContextLines(ctx.path, lines)}
              />
            ))}
          </ContextCards>

          {/* Chat messages */}
          <MessageList messages={messages} />

          {/* Smart input with @-mention */}
          <ChatInputWithMention
            onSend={sendMessage}
            onFileMention={handleFileMention}
            disabled={status !== 'ready'}
          />
        </div>
      </div>
    </div>
  );
}
```

- @-Mention Chat Input (`src/components/agent-chat-input.tsx`) — preserved from the plan
```typescript
export function ChatInputWithMention({ onSend, onFileMention, disabled }) {
  const [value, setValue] = useState('');
  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const [autocompleteQuery, setAutocompleteQuery] = useState('');
  const [caretPosition, setCaretPosition] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const caret = e.target.selectionStart;
    setValue(newValue);
    setCaretPosition(caret);

    // Detect @-mention pattern
    const beforeCaret = newValue.slice(0, caret);
    const mentionMatch = beforeCaret.match(/@([^\s]*)$/);

    if (mentionMatch) {
      setAutocompleteQuery(mentionMatch[1]);
      setShowAutocomplete(true);
    } else {
      setShowAutocomplete(false);
    }
  };

  const insertFileMention = useCallback((file: FileData) => {
    const beforeCaret = value.slice(0, caretPosition);
    const afterCaret = value.slice(caretPosition);

    // Find the @ symbol position
    const atPosition = beforeCaret.lastIndexOf('@');
    const beforeAt = value.slice(0, atPosition);

    // Create mention text
    const mentionText = `@${file.path}`;
    const newValue = beforeAt + mentionText + afterCaret;

    setValue(newValue);
    setShowAutocomplete(false);

    // Add file to context
    onFileMention(file.path);

    // Focus back to textarea
    textareaRef.current?.focus();
  }, [value, caretPosition, onFileMention]);

  return (
    <div className="chat-input-container">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="Type @ to mention files, ask questions, or request changes..."
        className="chat-input"
        disabled={disabled}
      />

      {showAutocomplete && (
        <FileAutocomplete
          query={autocompleteQuery}
          onSelect={insertFileMention}
          onClose={() => setShowAutocomplete(false)}
          position={getAutocompletePosition(textareaRef.current, caretPosition)}
        />
      )}
    </div>
  );
}
```

- File Autocomplete Component (`src/components/agent-file-autocomplete.tsx`) — preserved from the plan
```typescript
interface FileAutocompleteProps {
  query: string;
  onSelect: (file: FileData) => void;
  onClose: () => void;
  position: { top: number; left: number };
}

export function FileAutocomplete({ query, onSelect, onClose, position }: FileAutocompleteProps) {
  const [filteredFiles, setFilteredFiles] = useState<FileData[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    // Filter files based on query
    const filtered = searchFiles(query);
    setFilteredFiles(filtered.slice(0, 10)); // Limit to 10 results
  }, [query]);

  const handleKeyDown = (e: KeyboardEvent) => {
    switch(e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, filteredFiles.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        if (filteredFiles[selectedIndex]) {
          onSelect(filteredFiles[selectedIndex]);
        }
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  return (
    <div
      className="file-autocomplete-dropdown"
      style={{ top: position.top, left: position.left }}
    >
      {filteredFiles.map((file, index) => (
        <div
          key={file.path}
          className={`autocomplete-item ${index === selectedIndex ? 'selected' : ''}`}
          onMouseEnter={() => {
            setSelectedIndex(index);
            loadPreview(file.path).then(setPreview);
          }}
          onClick={() => onSelect(file)}
        >
          <FileIcon type={file.type} />
          <div className="file-info">
            <div className="file-path">{highlightMatch(file.path, query)}</div>
            <div className="file-meta">
              {file.size} • {file.tokenCount} tokens
            </div>
          </div>
        </div>
      ))}

      {preview && (
        <div className="file-preview-pane">
          <pre>{preview}</pre>
        </div>
      )}
    </div>
  );
}
```

- Agent attachments (message-scoped) — new example
```tsx
// In AgentPanel
const [pendingAttachments, setPendingAttachments] = useState<Map<string, AgentFileContext>>(new Map());
const [pinnedAttachments, setPinnedAttachments] = useState<Map<string, AgentFileContext>>(new Map());
const [pinEnabled, setPinEnabled] = useState(false);

const { messages, append, sendMessage, status } = useChat({
  api: '/api/v1/chat',
  onBeforeSend: (message) => {
    const pending = Array.from(pendingAttachments.values());
    const pinned = Array.from(pinnedAttachments.values());
    const byPath = new Map<string, AgentFileContext>([
      ...pinned.map(a => [a.path, a]),
      ...pending.map(a => [a.path, a])
    ]);
    const context = Array.from(byPath.values());
    return { ...message, context };
  },
  onFinish: () => {
    // Clear only the one-shot attachments
    setPendingAttachments(new Map());
    // Respect pin toggle
    if (!pinEnabled) setPinnedAttachments(new Map());
  }
});

// Selecting from @-autocomplete adds to pendingAttachments (not global selection)
const handleMentionSelect = async (path: string, lines?: LineRange) => {
  const file = await loadFileContent(path, lines); // validates workspace, denies binaries
  setPendingAttachments(prev => new Map(prev).set(path, file));
};

// Attachment list (chips/cards), removable, optional line editing
<AgentAttachmentList
  pending={pendingAttachments}
  pinned={pinnedAttachments}
  onRemove={(path) => setPendingAttachments(prev => { const n = new Map(prev); n.delete(path); return n; })}
  onPinToggle={(path, on) => setPinnedAttachments(prev => { const n = new Map(prev); const item = pendingAttachments.get(path) || prev.get(path); if (!item) return prev; if (on) n.set(path, item); else n.delete(path); return n; })}
  pinEnabled={pinEnabled}
  setPinEnabled={setPinEnabled}
/>;
```

- API Server: register route and handler — Phase 1 implementation

File: `src/main/api-server.ts`

Add the registration inside `registerRoutes()`:
```ts
// Chat (Vercel AI SDK v5)
this.app.post('/api/v1/chat', (req, res) => this.handleChat(req, res));
```

Add imports at the top of the file:
```ts
import { streamText, convertToModelMessages, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
```

Add the handler method to the class:
```ts
// Minimal Phase 1 chat handler with tool stubs
private async handleChat(req: Request, res: Response) {
  try {
    const body = (req.body ?? {}) as {
      messages?: unknown[];
      context?: unknown;
    };

    if (!Array.isArray(body.messages)) {
      return res.status(400).json(toApiError('VALIDATION_ERROR', 'Invalid body: messages[] required'));
    }

    const startedAt = Date.now();

    const result = await streamText({
      model: openai('gpt-5-mini'),
      messages: convertToModelMessages(body.messages as any[]),
      system: 'You are a coding assistant integrated with PasteFlow.',
      maxOutputTokens: 2048,
      temperature: 0.2,
      tools: {
        file: tool({
          description: 'File operations: read, info',
          inputSchema: z.object({
            action: z.enum(['read', 'info']),
            path: z.string()
          }),
          execute: async (input) => {
            const validation = validateAndResolvePath(input.path);
            if (!validation.ok) {
              return { error: 'PATH_DENIED', message: validation.message, reason: validation.reason };
            }

            if (input.action === 'info') {
              const st = await fileServiceStatFile(validation.absolutePath);
              if (!st.ok) return { error: st.code, message: st.message };

              const info: any = {
                name: st.data.name,
                path: st.data.path,
                size: st.data.size,
                isDirectory: st.data.isDirectory,
                isBinary: st.data.isBinary,
                mtimeMs: st.data.mtimeMs,
                fileType: st.data.fileType
              };

              if (!st.data.isDirectory && !st.data.isBinary) {
                const r = await readTextFile(st.data.path);
                if (r.ok && !r.isLikelyBinary) {
                  const { count } = await getMainTokenService().countTokens(r.content);
                  info.tokenCount = count;
                }
              }

              return info;
            }

            // action === 'read'
            const r = await readTextFile(validation.absolutePath);
            if (!r.ok) return { error: r.code, message: r.message };
            if (r.isLikelyBinary) return { error: 'BINARY_FILE', message: 'Binary file not supported' };

            const tokenService = getMainTokenService();
            const { count } = await tokenService.countTokens(r.content);
            return { path: validation.absolutePath, content: r.content, tokenCount: count };
          }
        }),

        search: tool({
          description: 'Filename search within workspace',
          inputSchema: z.object({
            action: z.literal('files'),
            query: z.string(),
            path: z.string().optional(),
            maxResults: z.number().optional()
          }),
          execute: async (input) => {
            const maxResults = typeof input.maxResults === 'number' ? Math.max(1, Math.min(500, input.maxResults)) : 200;
            const base = input.path && validateAndResolvePath(input.path).ok
              ? (validateAndResolvePath(input.path) as { ok: true; absolutePath: string }).absolutePath
              : (getAllowedWorkspacePaths()[0] ?? process.cwd());

            const results: { path: string; name: string }[] = [];
            const fs = nodeRequire('node:fs');
            const pathMod = nodeRequire('node:path');

            const walk = (dir: string) => {
              if (results.length >= maxResults) return;
              let entries: string[] = [];
              try { entries = fs.readdirSync(dir); } catch { return; }
              for (const name of entries) {
                if (results.length >= maxResults) break;
                const abs = pathMod.join(dir, name);
                let st: any;
                try { st = fs.statSync(abs); } catch { continue; }
                if (st.isDirectory()) {
                  walk(abs);
                } else if (name.toLowerCase().includes(input.query.toLowerCase())) {
                  results.push({ path: abs, name });
                }
              }
            };

            walk(base);
            return { query: input.query, results };
          }
        }),

        context: tool({
          description: 'Summarize provided dynamic context',
          inputSchema: z.object({ action: z.literal('summary') }),
          execute: async (_input, {}) => {
            const ctx = (req.body as any)?.context;
            if (!Array.isArray(ctx)) return { files: 0, tokens: 0 };
            let tokens = 0;
            for (const item of ctx) {
              if (item && typeof item.content === 'string') {
                try { tokens += (await getMainTokenService().countTokens(item.content)).count; } catch {}
              }
            }
            return { files: ctx.length, tokens };
          }
        })
      },
      streamOptions: {
        onFinish: async ({ usage }) => {
          const latencyMs = Date.now() - startedAt;
          // Optional: log usage locally; keep lightweight in Phase 1.
          try { await (this.db as any)?.insertLog?.({ category: 'chat', latencyMs, usage }); } catch {}
        }
      }
    });

    return result.pipeDataStreamToResponse(res);
  } catch (error) {
    return res.status(500).json(toApiError('INTERNAL_ERROR', (error as Error)?.message || 'Chat error'));
  }
}
```

- SendToAgentButton (`src/components/send-to-agent-button.tsx`)
```tsx
import { memo } from 'react';

interface SendToAgentButtonProps {
  enabled: boolean;
  packed: {
    files: { path: string; lines?: { start: number; end: number } | null; tokenCount?: number }[];
    instructions: string;
    prompts: { system: { id: string; name: string; content?: string }[]; role?: any[] };
    metadata: { workspace?: string | null; totalTokens: number; signature?: string; timestamp?: number };
  } | null;
}

export const SendToAgentButton = memo(function SendToAgentButton({ enabled, packed }: SendToAgentButtonProps) {
  const onClick = () => {
    if (!enabled || !packed) return;
    const ev = new CustomEvent('pasteflow:send-to-agent', { detail: packed });
    window.dispatchEvent(ev);
  };

  return (
    <button
      type="button"
      className="btn btn-primary"
      disabled={!enabled || !packed}
      onClick={onClick}
      title={enabled ? 'Send current packed content to Agent' : 'Pack content to enable sending'}
    >
      Send to Agent
    </button>
  );
});
```

- Integrate SendToAgentButton in Content Area (`src/components/content-area.tsx`)

Insert where the pack action UI is rendered and `isPacked` and `packedContent` (or equivalent) are available. For example:
```tsx
import { SendToAgentButton } from './send-to-agent-button';

// ... inside component render
{isPacked && (
  <SendToAgentButton enabled={true} packed={packedContent} />
)}
```

Non-invasive insertion example within the existing Ready state in `content-area.tsx`:
```tsx
{/* After Copy button, only when ready */}
{features?.SEND_TO_AGENT_ENABLED && packState.status === 'ready' && (
  <SendToAgentButton
    enabled={Boolean(packState.fullContent)}
    packed={{
      content: packState.fullContent || '',
      tokenEstimate: packState.tokenEstimate || 0,
      files: selectedFiles.length,
      metadata: { signature: packState.signature }
    }}
  />
)}
```


**Testing Strategy**

- Scope
  - Unit tests for tool input validation and file read behaviors.
  - Integration-style tests for `/api/v1/chat` using Jest with mocks for `ai` SDK and OpenAI provider, verifying streaming setup and route auth.
  - Renderer tests for `AgentPanel` event handling and `@` mention insertion logic.
  - Regression guardrails for file tree selection UI (checkboxes, partial states, auto-expand on folder check).

- Server: mock `ai` and provider, assert `pipeDataStreamToResponse` path
  - Location: `src/main/__tests__/api-chat.test.ts`
  - Approach:
    - Instantiate `PasteFlowAPIServer` with an in-memory DB bridge stub.
    - Monkeypatch `streamText` to return a fake object with `pipeDataStreamToResponse(res)` that writes a tiny SSE payload.
    - Validate: 401 without auth; 200 with auth; content-type set; request body validation on `messages`.
  - Example test skeleton:
```ts
import request from 'supertest';
import { PasteFlowAPIServer } from '../../main/api-server';

jest.mock('ai', () => ({
  streamText: jest.fn(() => ({
    pipeDataStreamToResponse: (res: any) => res.status(200).end('data: ok\n\n')
  })),
  convertToModelMessages: jest.fn((m: any) => m)
}));

describe('POST /api/v1/chat', () => {
  it('requires auth and messages array', async () => {
    const server = new PasteFlowAPIServer({} as any, 0);
    await server.startAsync();
    const port = server.getPort();

    // No auth
    await request(`http://127.0.0.1:${port}`).post('/api/v1/chat').send({}).expect(401);

    const token = server.getAuthToken();
    await request(`http://127.0.0.1:${port}`)
      .post('/api/v1/chat')
      .set('Authorization', token)
      .send({ messages: [{ role: 'user', content: 'hi' }] })
      .expect(200);

    server.close();
  });
});
```

- Server: tool unit tests
  - Location: `src/main/__tests__/tools-file-and-search.test.ts`
  - Approach: invoke tool `execute` with mock inputs; stub `validateAndResolvePath` and `readTextFile` using Jest spies; assert error paths and happy paths (e.g., directory rejection, binary detection).
  - Validate token counting with a known short string and ensure token service is called.

- Renderer: AgentPanel event and input tests
  - Location: `src/__tests__/agent-panel.test.tsx`
  - Approach:
    - Mock `@ai-sdk/react`’s `useChat` to capture calls to `append`/`sendMessage` and expose `messages` state.
    - Dispatch `pasteflow:send-to-agent` with a fabricated packed payload and assert `append` call shape.
    - Type `@` in the input, select an item in the autocomplete, and assert mention insertion in the textarea value and callback invocation.
  - Agent attachments tests (new)
    - Ensure selecting via @-autocomplete updates the Agent attachment store only (no calls to `toggleFileSelection`).
    - Verify `onBeforeSend` attaches pending + pinned attachments and that `onFinish` clears pending while respecting the pin toggle.
    - Assert that removing an attachment chip updates the store and does not mutate global selection.

- File Tree: regression tests (must continue to pass; add new ones if missing)
  - Existing suites to run unchanged:
    - `src/__tests__/tree-item-test.tsx` — verifies checkbox-only selection, chevron expansion, view button behavior.
    - `src/__tests__/file-tree-expansion-test.tsx` — verifies deterministic expand/collapse and auto-expand on folder select.
    - `src/__tests__/toggle-selection-functionality-test.tsx` — verifies selection state and token aggregation.
    - `src/__tests__/integration/file-selection-workflow-test.tsx` — end-to-end selection and copy flow.
  - Additions:
    - New: `agent-send-button-visibility.test.tsx` — assert `SendToAgentButton` only renders in ready state and dispatches `pasteflow:send-to-agent` without touching selection state.
    - New: `agent-panel-keyboard-scope.test.tsx` — mount AgentPanel and Sidebar together; confirm tree checkbox toggling via keyboard still works while typing in the Agent input (no global key interception).
    - New: `api-chat-rate-limit.test.ts` — ensure rate limiter returns 429 after N requests in window, without affecting other routes.


**Acceptance Criteria**

- API
  - `POST /api/v1/chat` exists, requires auth, validates `messages` array, and streams responses using `pipeDataStreamToResponse(res)`.
  - Uses `convertToModelMessages` to transform client messages.
  - Phase 1 tools available: `file.read`, `file.info`, `search.files`, `context.summary`.
  - Tool inputs validated via `zod`. File paths validated via `validateAndResolvePath` and only text files read via `readTextFile`.
  - Token counts obtained through `getMainTokenService().countTokens(text)` and included where relevant.

- UI
  - `AgentPanel` renders; can send and receive messages via `useChat`.
  - `@` mention dropdown appears on typing; selection inserts file reference text and triggers `onFileMention`.
  - `SendToAgentButton` visible when content is packed and dispatches a `pasteflow:send-to-agent` event with a detailed payload.
  - Agent chat supports Cursor-like file selection UX:
    - `@` autocomplete selects files into an Agent-only attachment list (chips/cards) without changing the main selection.
    - Attachments for the next message are applied via `onBeforeSend` and cleared (unless pinned) after send.
  - No regressions to file tree behavior: checkbox toggles, partial/indeterminate states, folder auto-expand on selection, and keyboard interactions continue to work as before.

- Security & Resilience
  - Route inherits existing auth middleware.
  - File tool refuses directories and binary files; respects workspace boundaries.
  - Search tool limited to filename match with a bounded result set and safe recursion.
  - Errors surfaced as JSON objects in tool responses without throwing uncaught exceptions.

- Dev Experience
  - Code compiles with new deps.
  - Tests pass locally with mocks.
  - All existing file tree–related tests pass unchanged.


**Future Phase Context**

- Phase 2: Dual-Context System
  - Move beyond minimal `context.summary` by integrating richer context composition and per-turn summaries.
  - Enhance `AgentPanel` token displays and context cards, plus better autocomplete heuristics.

- Phase 3: Terminal Management and Expanded Tools
  - Introduce terminal/process tools with persistent sessions and output streaming.
  - Add explicit `code` search via ripgrep JSON, with batched paging for large results.

- Phase 4: Advanced Generation & Editing
  - Add structured edit tools with preview/apply flows, multi-file changes, and diffs.
  - Route-level observability and persistence of usage, latencies, and tool metrics.

These phases build on Phase 1’s core chat and tool scaffolding. The API route, UI transport, and tool surface established here are intentionally designed to accommodate subsequent expansion without breaking changes.
